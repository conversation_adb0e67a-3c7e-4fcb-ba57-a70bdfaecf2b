/* User Dashboard Header - Based on existing Header.css */
.user-dashboard-header {
  position: fixed;
  top: 24px;
  left: 120px;
  right: 120px;
  height: 72px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 999px;
  z-index: 1000;
  box-sizing: border-box;
}

.user-dashboard-header .header-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* Logo */
.user-dashboard-header .logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: 38px;
  flex: 0 0 auto;
  margin-right: 40px;
}

.user-dashboard-header .logo-image {
  height: 38px;
  width: auto;
  object-fit: contain;
}

/* Navigation Links */
.user-dashboard-header .nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0px;
  gap: 32px;
  height: 28px;
  flex: 1;
}

.user-dashboard-header .nav-link {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  height: 28px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 999px;
  transition: all 0.3s ease;
  flex: none;
  flex-grow: 0;
}

.user-dashboard-header .nav-link:hover {
  color: var(--text-primary);
  background: rgba(64, 64, 64, 0.5);
}

.user-dashboard-header .nav-link.active {
  background: var(--border-color);
  border: 1px solid var(--border-secondary);
  color: var(--text-primary);
  font-weight: 500;
  width: 66px;
}

/* Header Actions - User Profile */
.user-dashboard-header .header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 0px;
  gap: 16px;
  height: 48px;
  flex: 0 0 auto;
}

/* User Profile Dropdown */
.user-profile-dropdown {
  position: relative;
}

.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 999px;
  transition: all 0.3s ease;
}

.profile-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #BF4129;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
}

.avatar-text {
  color: #FFFFFF;
}

.dropdown-arrow {
  color: #CCCCCC;
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

/* Profile Dropdown Menu */
.profile-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: rgba(38, 38, 38, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px 0;
  min-width: 180px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1001;
}

.dropdown-item {
  padding: 12px 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

.dropdown-item.logout {
  color: #EF4444;
}

.dropdown-item.logout:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}

.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 8px 0;
}

/* Mobile Menu Toggle */
.user-dashboard-header .mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.user-dashboard-header .hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

/* Mobile Menu */
.user-dashboard-header .mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1a1a1a;
  border-radius: 0 0 20px 20px;
  padding: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-dashboard-header .mobile-menu-open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.user-dashboard-header .mobile-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.user-dashboard-header .mobile-nav-link {
  padding: 12px 16px;
  background: rgba(64, 64, 64, 0.5);
  border: 1px solid rgba(82, 82, 82, 0.5);
  border-radius: 999px;
  color: var(--text-secondary);
  text-decoration: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.user-dashboard-header .mobile-nav-link:hover {
  background: rgba(64, 64, 64, 0.8);
  color: var(--text-primary);
}

.user-dashboard-header .mobile-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.mobile-user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(64, 64, 64, 0.5);
  border: 1px solid rgba(82, 82, 82, 0.5);
  border-radius: 999px;
}

.mobile-user-profile .profile-name {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
}

/* Responsive Design */
@media (min-width: 1201px) {
  .user-dashboard-header .logo {
    transform: translateX(-110px);
  }

  .user-dashboard-header .header-actions {
    transform: translateX(140px);
  }
}

@media (max-width: 1200px) {
  .user-dashboard-header {
    left: 60px;
    right: 60px;
  }

  .user-dashboard-header .header-content {
    gap: 150px;
  }

  .user-dashboard-header .nav {
    width: 400px;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .user-dashboard-header {
    left: 20px;
    right: 20px;
    height: 60px;
  }

  .user-dashboard-header .header-content {
    padding: 8px 16px;
    gap: 20px;
  }

  .user-dashboard-header .logo {
    width: auto;
    margin-right: 0;
  }

  .user-dashboard-header .logo-image {
    height: 30px;
  }

  /* Hide desktop navigation and actions */
  .user-dashboard-header .desktop-nav,
  .user-dashboard-header .desktop-actions {
    display: none;
  }

  /* Show mobile menu toggle */
  .user-dashboard-header .mobile-menu-toggle {
    display: flex;
  }

  /* Show mobile menu */
  .user-dashboard-header .mobile-menu {
    display: block;
  }
}

@media (max-width: 480px) {
  .user-dashboard-header {
    left: 10px;
    right: 10px;
    top: 12px;
  }

  .user-dashboard-header .logo-image {
    height: 24px;
  }

  .user-dashboard-header .mobile-menu {
    padding: 16px;
  }

  .user-dashboard-header .mobile-nav-link {
    padding: 10px 12px;
    font-size: 14px;
  }
}

/* SideNav Component Styles */
.sidenav-container {
  width: 280px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidenav-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidenav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
}

.sidenav-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.sidenav-item.active {
  background: #BF4129;
  color: #FFFFFF;
}

.sidenav-item.active:hover {
  background: #BF4129;
}

.sidenav-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  filter: brightness(0.8);
}

.sidenav-item.active .sidenav-icon {
  filter: brightness(1);
}

.sidenav-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidenav-container {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 8px 12px;
    margin-bottom: 20px;
    margin-top: -10px; /* Shift sidenav up on mobile */
    border-radius: 12px;
    height: 60px;
    display: flex;
    align-items: center;
  }

  .sidenav-list {
    flex-direction: row;
    overflow-x: auto;
    gap: 6px;
    padding: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    scroll-behavior: smooth;
  }

  .sidenav-list::-webkit-scrollbar {
    display: none;
  }

  .sidenav-item {
    white-space: nowrap;
    min-width: 110px;
    max-width: 120px;
    padding: 8px 12px;
    flex-shrink: 0;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    box-sizing: border-box;
  }

  .sidenav-icon {
    width: 18px;
    height: 18px;
  }

  .sidenav-label {
    font-size: 11px;
  }
}

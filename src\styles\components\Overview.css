/* Overview Content */
.overview-content {
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar styles removed - using SideNav component */

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.overview-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.dashboard-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: -42px;
  align-items: flex-start;
  align-content: flex-start;
}

/* Dashboard Cards */
.dashboard-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  width: calc(50% - 12px);
  min-height: 160px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
  line-height: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.card-unit {
  font-size: 18px;
  font-weight: 500;
  color: #CCCCCC;
}

.card-change {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.change-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-change.positive {
  color: #4CAF50;
}

.card-change.negative {
  color: #FF6B6B;
}



/* Mobile Responsive */
@media (max-width: 768px) {

  .page-header {
    padding: 20px 20px 20px !important; /* Reduced padding for compactness */
  }

  .page-title {
    font-size: 28px !important; /* Significantly reduced title size */
    gap: 8px;
    justify-content: center;
    margin-bottom: 12px !important; /* Reduced margin */
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 120px; /* Smaller decorative span */
  }

  .page-subtitle {
    font-size: 12px !important; /* Smaller subtitle */
    line-height: 18px !important; /* Tighter line height */
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 16px !important; /* Reduced horizontal padding */
    gap: 16px !important; /* Reduced gap */
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 12px !important; /* Reduced gap */
  }

  /* Remove conflicting sidebar styles - SideNav component handles its own mobile styles */

  .dashboard-content {
    order: 2;
    max-width: 100%;
    width: 100%;
  }

  .dashboard-cards {
    gap: 12px !important; /* Reduced card spacing */
    margin-top: -20px !important; /* Reduced top margin */
  }

  .dashboard-card {
    width: 100%;
    padding: 16px !important; /* Reduced card padding */
    min-height: 120px !important; /* Reduced minimum height */
    border-radius: 12px !important; /* Smaller border radius */
  }

  .card-value {
    font-size: 20px !important; /* Smaller card values */
  }

  .card-title {
    font-size: 11px !important; /* Smaller card titles */
  }

  .card-unit {
    font-size: 14px !important; /* Smaller units */
  }

  .card-change {
    font-size: 13px !important; /* Smaller change indicators */
  }

  /* Additional mobile optimizations */
  .section-title {
    font-size: 18px !important; /* Smaller section titles */
    margin-bottom: 16px !important; /* Reduced margin */
  }

  .overview-header {
    margin-bottom: 20px !important; /* Reduced header margin */
  }

  /* Ensure proper spacing and prevent clashing */
  .dashboard-mode-tabs {
    margin-bottom: 12px !important; /* Reduced margin */
  }

  /* Prevent content from being too close to edges */
  .overview-content {
    padding-bottom: 20px !important; /* Add bottom padding */
  }
}